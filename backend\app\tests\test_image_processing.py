"""
Unit tests for image processing utilities.
"""

import pytest
import numpy as np
import base64
import sys
from pathlib import Path
from io import BytesIO
from PIL import Image

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.app.utils.image import ImageProcessor, ImageProcessingError

class TestImageProcessor:
    """Test cases for ImageProcessor class."""
    
    def test_initialization(self):
        """Test ImageProcessor initialization."""
        processor = ImageProcessor()
        assert processor is not None
        assert processor.SUPPORTED_FORMATS == ['JPEG', 'PNG', 'WebP', 'BMP']
        assert processor.MAX_DIMENSION == 2048
        assert processor.MIN_DIMENSION == 100
    
    def test_decode_base64_image_success(self, sample_base64_image):
        """Test successful base64 image decoding."""
        processor = ImageProcessor()
        
        result = processor.decode_base64_image(sample_base64_image)
        
        assert isinstance(result, np.ndarray)
        assert len(result.shape) == 3  # Height, Width, <PERSON>s
        assert result.shape[2] == 3  # BGR channels
    
    def test_decode_base64_image_with_data_url(self, sample_base64_image):
        """Test decoding base64 image with data URL prefix."""
        processor = ImageProcessor()
        
        # Add data URL prefix
        data_url = f"data:image/png;base64,{sample_base64_image}"
        
        result = processor.decode_base64_image(data_url)
        
        assert isinstance(result, np.ndarray)
        assert len(result.shape) == 3
    
    def test_decode_base64_image_invalid_data(self):
        """Test decoding invalid base64 data."""
        processor = ImageProcessor()
        
        with pytest.raises(ImageProcessingError):
            processor.decode_base64_image("invalid_base64_data")
    
    def test_decode_base64_image_empty_data(self):
        """Test decoding empty base64 data."""
        processor = ImageProcessor()
        
        with pytest.raises(ImageProcessingError):
            processor.decode_base64_image("")
    
    def test_encode_image_to_base64(self, sample_image_array):
        """Test encoding image to base64."""
        processor = ImageProcessor()
        
        result = processor.encode_image_to_base64(sample_image_array)
        
        assert isinstance(result, str)
        assert len(result) > 0
        
        # Verify it can be decoded back
        decoded = processor.decode_base64_image(result)
        assert decoded.shape == sample_image_array.shape
    
    def test_validate_and_resize_normal_image(self, sample_image_array):
        """Test validation and resizing of normal-sized image."""
        processor = ImageProcessor()
        
        result = processor.validate_and_resize(sample_image_array)
        
        assert isinstance(result, np.ndarray)
        assert result.shape == sample_image_array.shape  # Should not be resized
    
    def test_validate_and_resize_large_image(self):
        """Test validation and resizing of large image."""
        processor = ImageProcessor()
        
        # Create a large image
        large_image = np.zeros((3000, 3000, 3), dtype=np.uint8)
        
        result = processor.validate_and_resize(large_image)
        
        assert isinstance(result, np.ndarray)
        assert max(result.shape[:2]) <= processor.MAX_DIMENSION
    
    def test_validate_and_resize_small_image(self):
        """Test validation of too-small image."""
        processor = ImageProcessor()
        
        # Create a small image
        small_image = np.zeros((50, 50, 3), dtype=np.uint8)
        
        with pytest.raises(ImageProcessingError):
            processor.validate_and_resize(small_image)
    
    def test_enhance_image(self, sample_image_array):
        """Test image enhancement."""
        processor = ImageProcessor()
        
        result = processor.enhance_image(sample_image_array)
        
        assert isinstance(result, np.ndarray)
        assert result.shape == sample_image_array.shape
    
    def test_preprocess_for_detection(self, sample_image_array):
        """Test image preprocessing for detection."""
        processor = ImageProcessor()
        
        result = processor.preprocess_for_detection(sample_image_array)
        
        assert isinstance(result, np.ndarray)
        assert len(result.shape) == 3
        assert result.shape[2] == 3  # Should remain BGR
    
    def test_get_image_info(self, sample_image_array):
        """Test getting image information."""
        processor = ImageProcessor()
        
        info = processor.get_image_info(sample_image_array)
        
        assert isinstance(info, dict)
        assert 'width' in info
        assert 'height' in info
        assert 'channels' in info
        assert 'dtype' in info
        assert 'size_bytes' in info
        assert 'aspect_ratio' in info
        
        assert info['width'] == sample_image_array.shape[1]
        assert info['height'] == sample_image_array.shape[0]
        assert info['channels'] == sample_image_array.shape[2]
    
    def test_get_image_info_grayscale(self):
        """Test getting info for grayscale image."""
        processor = ImageProcessor()
        
        # Create grayscale image
        gray_image = np.zeros((100, 200), dtype=np.uint8)
        
        info = processor.get_image_info(gray_image)
        
        assert info['channels'] == 1
        assert info['width'] == 200
        assert info['height'] == 100
    
    def test_error_handling_in_enhancement(self):
        """Test error handling in image enhancement."""
        processor = ImageProcessor()
        
        # Create invalid image data
        invalid_image = np.array([])
        
        # Should return original image on enhancement failure
        result = processor.enhance_image(invalid_image)
        assert np.array_equal(result, invalid_image)
    
    def test_supported_formats_validation(self):
        """Test that only supported formats are accepted."""
        processor = ImageProcessor()
        
        # Create a test image in an unsupported format would require
        # more complex setup, so we test the format list instead
        assert 'JPEG' in processor.SUPPORTED_FORMATS
        assert 'PNG' in processor.SUPPORTED_FORMATS
        assert 'WebP' in processor.SUPPORTED_FORMATS
        assert 'BMP' in processor.SUPPORTED_FORMATS
