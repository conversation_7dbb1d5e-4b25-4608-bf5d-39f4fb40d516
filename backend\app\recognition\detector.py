import cv2
import numpy as np
from typing import List, Dict, Optional
from dataclasses import dataclass

@dataclass
class UIElement:
    """Represents a detected UI element with its properties."""
    type: str
    bbox: tuple  # (x, y, width, height)
    text: Optional[str] = None
    style: Dict[str, str] = None
    children: List['UIElement'] = None
    confidence: float = 1.0

    def __post_init__(self):
        if self.style is None:
            self.style = {}
        if self.children is None:
            self.children = []

class UIElementDetector:
    """Detects UI elements in screenshots using computer vision and ML techniques."""
    
    def __init__(self):
        # Initialize detection parameters
        self.min_confidence = 0.7
        self.supported_elements = ['button', 'input', 'text', 'image', 'container']

    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """Preprocesses the input image for better element detection."""
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        # Apply adaptive thresholding
        thresh = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY_INV, 11, 2
        )
        return thresh

    def detect_elements(self, image: np.ndarray) -> List[UIElement]:
        """Detects UI elements in the preprocessed image."""
        elements = []
        processed = self.preprocess_image(image)
        
        # Find contours for element detection
        contours, _ = cv2.findContours(
            processed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
        )
        
        for cnt in contours:
            # Get bounding box
            x, y, w, h = cv2.boundingRect(cnt)
            
            # Basic element classification based on shape and size
            element_type = self._classify_element(w, h, cnt)
            if element_type:
                element = UIElement(
                    type=element_type,
                    bbox=(x, y, w, h),
                    style=self._extract_style(image[y:y+h, x:x+w])
                )
                elements.append(element)
        
        return elements

    def _classify_element(self, width: int, height: int, contour: np.ndarray) -> Optional[str]:
        """Classifies UI element based on shape and size characteristics."""
        aspect_ratio = width / float(height) if height > 0 else 0
        area = cv2.contourArea(contour)
        
        if width < 10 or height < 10:
            return None
            
        if 2.5 <= aspect_ratio <= 5 and area > 1000:
            return 'input'
        elif 0.5 <= aspect_ratio <= 2 and area > 500:
            return 'button'
        elif aspect_ratio > 5:
            return 'text'
        elif 0.8 <= aspect_ratio <= 1.2 and area > 2000:
            return 'image'
        
        return 'container'

    def _extract_style(self, element_image: np.ndarray) -> Dict[str, str]:
        """Extracts style properties from the element image."""
        style = {}
        
        # Extract background color
        if element_image.size > 0:
            mean_color = cv2.mean(element_image)[:3]
            style['background-color'] = f'rgb({int(mean_color[2])}, {int(mean_color[1])}, {int(mean_color[0])})'"