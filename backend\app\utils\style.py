"""
SnapCode Style Extraction Utilities

This module provides advanced style extraction capabilities including color analysis,
typography detection, spacing calculations, and other style-related operations.
"""

import logging
from typing import Dict, List, Tuple, Optional, Any
import numpy as np
import cv2
from collections import Counter
from sklearn.cluster import KMeans
import colorsys

logger = logging.getLogger(__name__)

class StyleExtractionError(Exception):
    """Custom exception for style extraction errors."""
    pass

class StyleExtractor:
    """Extracts style information from UI elements and images."""
    
    def __init__(self):
        """Initialize the style extractor."""
        self.logger = logging.getLogger(__name__)
        
        # Common UI color mappings
        self.color_names = {
            (255, 255, 255): 'white',
            (0, 0, 0): 'black',
            (128, 128, 128): 'gray',
            (255, 0, 0): 'red',
            (0, 255, 0): 'green',
            (0, 0, 255): 'blue',
            (255, 255, 0): 'yellow',
            (255, 0, 255): 'magenta',
            (0, 255, 255): 'cyan',
        }
    
    def extract_dominant_colors(self, image: np.ndarray, k: int = 5) -> List[Dict[str, Any]]:
        """
        Extract dominant colors from an image using K-means clustering.
        
        Args:
            image: Input image as numpy array
            k: Number of dominant colors to extract
            
        Returns:
            List of color information dictionaries
        """
        try:
            # Reshape image to be a list of pixels
            pixels = image.reshape(-1, 3)
            
            # Remove pure black and white pixels (likely background/text)
            mask = ~((pixels == [0, 0, 0]).all(axis=1) | (pixels == [255, 255, 255]).all(axis=1))
            filtered_pixels = pixels[mask]
            
            if len(filtered_pixels) == 0:
                return []
            
            # Apply K-means clustering
            kmeans = KMeans(n_clusters=min(k, len(filtered_pixels)), random_state=42, n_init=10)
            kmeans.fit(filtered_pixels)
            
            colors = []
            for i, color in enumerate(kmeans.cluster_centers_):
                # Convert BGR to RGB
                rgb_color = (int(color[2]), int(color[1]), int(color[0]))
                
                # Calculate color properties
                color_info = self._analyze_color(rgb_color)
                color_info['frequency'] = len(kmeans.labels_[kmeans.labels_ == i]) / len(kmeans.labels_)
                
                colors.append(color_info)
            
            # Sort by frequency
            colors.sort(key=lambda x: x['frequency'], reverse=True)
            
            self.logger.debug(f"Extracted {len(colors)} dominant colors")
            return colors
            
        except Exception as e:
            self.logger.error(f"Failed to extract dominant colors: {str(e)}")
            return []
    
    def _analyze_color(self, rgb_color: Tuple[int, int, int]) -> Dict[str, Any]:
        """
        Analyze a color and return comprehensive information.
        
        Args:
            rgb_color: RGB color tuple
            
        Returns:
            Dictionary with color analysis
        """
        r, g, b = rgb_color
        
        # Convert to different color spaces
        hsv = colorsys.rgb_to_hsv(r/255, g/255, b/255)
        hls = colorsys.rgb_to_hls(r/255, g/255, b/255)
        
        # Calculate luminance
        luminance = 0.299 * r + 0.587 * g + 0.114 * b
        
        # Determine if color is light or dark
        is_light = luminance > 128
        
        # Find closest named color
        closest_name = self._get_closest_color_name(rgb_color)
        
        return {
            'rgb': rgb_color,
            'hex': f'#{r:02x}{g:02x}{b:02x}',
            'hsv': {
                'hue': int(hsv[0] * 360),
                'saturation': int(hsv[1] * 100),
                'value': int(hsv[2] * 100)
            },
            'hls': {
                'hue': int(hls[0] * 360),
                'lightness': int(hls[1] * 100),
                'saturation': int(hls[2] * 100)
            },
            'luminance': luminance,
            'is_light': is_light,
            'name': closest_name,
            'css': f'rgb({r}, {g}, {b})'
        }
    
    def _get_closest_color_name(self, rgb_color: Tuple[int, int, int]) -> str:
        """
        Find the closest named color to the given RGB color.
        
        Args:
            rgb_color: RGB color tuple
            
        Returns:
            Name of the closest color
        """
        min_distance = float('inf')
        closest_name = 'unknown'
        
        for named_rgb, name in self.color_names.items():
            # Calculate Euclidean distance in RGB space
            distance = sum((a - b) ** 2 for a, b in zip(rgb_color, named_rgb)) ** 0.5
            if distance < min_distance:
                min_distance = distance
                closest_name = name
        
        return closest_name
    
    def extract_element_style(self, element_image: np.ndarray, bbox: Tuple[int, int, int, int]) -> Dict[str, Any]:
        """
        Extract style properties from a UI element.
        
        Args:
            element_image: Image of the UI element
            bbox: Bounding box (x, y, width, height)
            
        Returns:
            Dictionary with extracted style properties
        """
        try:
            style = {}
            
            if element_image.size == 0:
                return style
            
            # Extract colors
            dominant_colors = self.extract_dominant_colors(element_image, k=3)
            if dominant_colors:
                style['background_color'] = dominant_colors[0]['css']
                style['background_color_hex'] = dominant_colors[0]['hex']
                
                # Determine text color based on background
                bg_luminance = dominant_colors[0]['luminance']
                style['text_color'] = 'white' if bg_luminance < 128 else 'black'
            
            # Extract dimensions and positioning
            x, y, width, height = bbox
            style['width'] = width
            style['height'] = height
            style['aspect_ratio'] = width / height if height > 0 else 1
            
            # Analyze borders and edges
            border_info = self._detect_borders(element_image)
            style.update(border_info)
            
            # Analyze spacing and padding
            spacing_info = self._analyze_spacing(element_image)
            style.update(spacing_info)
            
            # Determine element type styling
            element_type_style = self._infer_element_type_style(element_image, bbox)
            style.update(element_type_style)
            
            self.logger.debug(f"Extracted style properties: {len(style)} properties")
            return style
            
        except Exception as e:
            self.logger.error(f"Failed to extract element style: {str(e)}")
            return {}
    
    def _detect_borders(self, image: np.ndarray) -> Dict[str, Any]:
        """
        Detect border properties of an element.
        
        Args:
            image: Element image
            
        Returns:
            Dictionary with border properties
        """
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Detect edges
            edges = cv2.Canny(gray, 50, 150)
            
            # Check for rectangular borders
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            border_info = {
                'has_border': False,
                'border_width': 0,
                'border_style': 'none',
                'border_radius': 0
            }
            
            if contours:
                # Find the largest contour (likely the border)
                largest_contour = max(contours, key=cv2.contourArea)
                
                # Approximate contour to polygon
                epsilon = 0.02 * cv2.arcLength(largest_contour, True)
                approx = cv2.approxPolyDP(largest_contour, epsilon, True)
                
                # Check if it's roughly rectangular (4 corners)
                if len(approx) >= 4:
                    border_info['has_border'] = True
                    border_info['border_style'] = 'solid'
                    
                    # Estimate border width (simplified)
                    border_info['border_width'] = max(1, int(np.mean(edges) * 0.1))
                    
                    # Estimate border radius (simplified)
                    if len(approx) > 4:
                        border_info['border_radius'] = min(image.shape[:2]) // 10
            
            return border_info
            
        except Exception as e:
            self.logger.warning(f"Border detection failed: {str(e)}")
            return {'has_border': False, 'border_width': 0, 'border_style': 'none', 'border_radius': 0}
    
    def _analyze_spacing(self, image: np.ndarray) -> Dict[str, Any]:
        """
        Analyze spacing and padding within an element.
        
        Args:
            image: Element image
            
        Returns:
            Dictionary with spacing properties
        """
        try:
            height, width = image.shape[:2]
            
            # Simple heuristic for padding based on element size
            padding = max(2, min(width, height) // 20)
            
            return {
                'padding': padding,
                'padding_top': padding,
                'padding_right': padding,
                'padding_bottom': padding,
                'padding_left': padding,
                'margin': padding // 2
            }
            
        except Exception as e:
            self.logger.warning(f"Spacing analysis failed: {str(e)}")
            return {'padding': 4, 'margin': 2}
    
    def _infer_element_type_style(self, image: np.ndarray, bbox: Tuple[int, int, int, int]) -> Dict[str, Any]:
        """
        Infer additional style properties based on element characteristics.
        
        Args:
            image: Element image
            bbox: Bounding box
            
        Returns:
            Dictionary with inferred style properties
        """
        try:
            x, y, width, height = bbox
            aspect_ratio = width / height if height > 0 else 1
            
            style = {}
            
            # Button-like elements
            if 0.5 <= aspect_ratio <= 4 and width > 60 and height > 20:
                style['cursor'] = 'pointer'
                style['user_select'] = 'none'
                style['transition'] = 'all 0.2s ease'
            
            # Input-like elements
            elif 2 <= aspect_ratio <= 8 and height >= 30:
                style['border'] = '1px solid #ccc'
                style['outline'] = 'none'
                style['transition'] = 'border-color 0.2s ease'
            
            # Text-like elements
            elif aspect_ratio > 5:
                style['line_height'] = '1.5'
                style['word_wrap'] = 'break-word'
            
            # Container-like elements
            elif width > 200 and height > 100:
                style['display'] = 'flex'
                style['flex_direction'] = 'column'
            
            return style
            
        except Exception as e:
            self.logger.warning(f"Element type style inference failed: {str(e)}")
            return {}
    
    def generate_css_classes(self, styles: Dict[str, Any], framework: str = 'tailwind') -> List[str]:
        """
        Generate CSS classes based on extracted styles.
        
        Args:
            styles: Extracted style properties
            framework: CSS framework to use
            
        Returns:
            List of CSS classes
        """
        try:
            classes = []
            
            if framework == 'tailwind':
                classes.extend(self._generate_tailwind_classes(styles))
            elif framework == 'bootstrap':
                classes.extend(self._generate_bootstrap_classes(styles))
            else:
                classes.extend(self._generate_vanilla_classes(styles))
            
            return classes
            
        except Exception as e:
            self.logger.error(f"Failed to generate CSS classes: {str(e)}")
            return []
    
    def _generate_tailwind_classes(self, styles: Dict[str, Any]) -> List[str]:
        """Generate Tailwind CSS classes from styles."""
        classes = []
        
        # Background color
        if 'background_color_hex' in styles:
            classes.append(f'bg-[{styles["background_color_hex"]}]')
        
        # Text color
        if 'text_color' in styles:
            if styles['text_color'] == 'white':
                classes.append('text-white')
            elif styles['text_color'] == 'black':
                classes.append('text-black')
        
        # Padding
        if 'padding' in styles:
            padding = styles['padding']
            if padding <= 4:
                classes.append('p-1')
            elif padding <= 8:
                classes.append('p-2')
            elif padding <= 16:
                classes.append('p-4')
            else:
                classes.append('p-6')
        
        # Border radius
        if styles.get('border_radius', 0) > 0:
            classes.append('rounded')
        
        # Cursor
        if styles.get('cursor') == 'pointer':
            classes.append('cursor-pointer')
        
        return classes
    
    def _generate_bootstrap_classes(self, styles: Dict[str, Any]) -> List[str]:
        """Generate Bootstrap CSS classes from styles."""
        classes = []
        
        # Basic Bootstrap classes
        if 'cursor' in styles and styles['cursor'] == 'pointer':
            classes.append('btn')
        
        return classes
    
    def _generate_vanilla_classes(self, styles: Dict[str, Any]) -> List[str]:
        """Generate vanilla CSS classes from styles."""
        return ['custom-element']
