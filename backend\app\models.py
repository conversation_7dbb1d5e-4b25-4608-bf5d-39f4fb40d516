"""
SnapCode API Models and Schemas

This module defines Pydantic models for API request/response schemas,
data validation, and serialization for the SnapCode application.
"""

from typing import List, Dict, Optional, Any, Union
from pydantic import BaseModel, Field, validator
from enum import Enum
import base64
import re

class FrameworkType(str, Enum):
    """Supported code generation frameworks."""
    HTML = "html"
    REACT = "react"
    VUE = "vue"
    SWIFT = "swift"

class StylingFramework(str, Enum):
    """Supported CSS/styling frameworks."""
    TAILWIND = "tailwind"
    BOOTSTRAP = "bootstrap"
    VANILLA = "vanilla"
    STYLED_COMPONENTS = "styled-components"

class ConversionOptions(BaseModel):
    """Options for code conversion process."""
    styling: StylingFramework = StylingFramework.TAILWIND
    typescript: bool = True
    responsive: bool = True
    accessibility: bool = True
    optimize: bool = True
    include_comments: bool = True
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True

class ConversionRequest(BaseModel):
    """Request model for screenshot to code conversion."""
    image: str = Field(..., description="Base64 encoded image data")
    framework: FrameworkType = Field(FrameworkType.REACT, description="Target framework for code generation")
    options: ConversionOptions = Field(default_factory=ConversionOptions, description="Conversion options")
    
    @validator("image")
    def validate_image(cls, v):
        """Validate base64 image data."""
        if not v:
            raise ValueError("Image data is required")
        
        # Check if it's a valid base64 string
        try:
            # Remove data URL prefix if present
            if v.startswith("data:"):
                v = v.split(",", 1)[1]
            
            # Validate base64 encoding
            base64.b64decode(v)
            return v
        except Exception:
            raise ValueError("Invalid base64 image data")
    
    @validator("framework", pre=True)
    def validate_framework(cls, v):
        """Validate framework type."""
        if isinstance(v, str):
            return FrameworkType(v.lower())
        return v
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        schema_extra = {
            "example": {
                "image": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
                "framework": "react",
                "options": {
                    "styling": "tailwind",
                    "typescript": True,
                    "responsive": True,
                    "accessibility": True,
                    "optimize": True,
                    "include_comments": True
                }
            }
        }

class UIElementInfo(BaseModel):
    """Information about a detected UI element."""
    type: str = Field(..., description="Type of UI element (button, input, text, etc.)")
    bbox: List[int] = Field(..., description="Bounding box coordinates [x, y, width, height]")
    text: Optional[str] = Field(None, description="Text content of the element")
    style: Dict[str, Any] = Field(default_factory=dict, description="Style properties")
    confidence: float = Field(1.0, ge=0.0, le=1.0, description="Detection confidence score")
    children: List['UIElementInfo'] = Field(default_factory=list, description="Child elements")
    
    class Config:
        """Pydantic configuration."""
        schema_extra = {
            "example": {
                "type": "button",
                "bbox": [10, 20, 100, 40],
                "text": "Click me",
                "style": {"background-color": "rgb(59, 130, 246)", "color": "white"},
                "confidence": 0.95,
                "children": []
            }
        }

# Enable forward references for recursive model
UIElementInfo.model_rebuild()

class ConversionResult(BaseModel):
    """Result of the code conversion process."""
    code: str = Field(..., description="Generated code")
    framework: FrameworkType = Field(..., description="Framework used for generation")
    components: List[UIElementInfo] = Field(default_factory=list, description="Detected UI components")
    styles: Dict[str, Any] = Field(default_factory=dict, description="Extracted styles")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        schema_extra = {
            "example": {
                "code": "import React from 'react';\n\nconst GeneratedComponent = () => {\n  return (\n    <div className=\"p-4\">\n      <button className=\"bg-blue-500 text-white px-4 py-2 rounded\">\n        Click me\n      </button>\n    </div>\n  );\n};\n\nexport default GeneratedComponent;",
                "framework": "react",
                "components": [
                    {
                        "type": "button",
                        "bbox": [10, 20, 100, 40],
                        "text": "Click me",
                        "style": {"background-color": "rgb(59, 130, 246)", "color": "white"},
                        "confidence": 0.95,
                        "children": []
                    }
                ],
                "styles": {"primary-color": "#3b82f6", "text-color": "#ffffff"},
                "metadata": {"processing_time": 1.23, "elements_detected": 1}
            }
        }

class ConversionResponse(BaseModel):
    """Response model for conversion API."""
    success: bool = Field(..., description="Whether the conversion was successful")
    result: Optional[ConversionResult] = Field(None, description="Conversion result if successful")
    error: Optional[str] = Field(None, description="Error message if conversion failed")
    request_id: Optional[str] = Field(None, description="Unique request identifier")
    
    class Config:
        """Pydantic configuration."""
        schema_extra = {
            "example": {
                "success": True,
                "result": {
                    "code": "import React from 'react';\n\nconst GeneratedComponent = () => {\n  return (\n    <div className=\"p-4\">\n      <button className=\"bg-blue-500 text-white px-4 py-2 rounded\">\n        Click me\n      </button>\n    </div>\n  );\n};\n\nexport default GeneratedComponent;",
                    "framework": "react",
                    "components": [],
                    "styles": {},
                    "metadata": {}
                },
                "error": None,
                "request_id": "req_123456789"
            }
        }

class HealthCheckResponse(BaseModel):
    """Health check response model."""
    status: str = Field(..., description="Health status")
    version: str = Field(..., description="Application version")
    timestamp: str = Field(..., description="Current timestamp")
    uptime: float = Field(..., description="Application uptime in seconds")
    
    class Config:
        """Pydantic configuration."""
        schema_extra = {
            "example": {
                "status": "healthy",
                "version": "1.0.0",
                "timestamp": "2024-01-01T12:00:00Z",
                "uptime": 3600.0
            }
        }

class ErrorResponse(BaseModel):
    """Error response model."""
    error: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Detailed error information")
    code: Optional[str] = Field(None, description="Error code")
    request_id: Optional[str] = Field(None, description="Request identifier")
    
    class Config:
        """Pydantic configuration."""
        schema_extra = {
            "example": {
                "error": "Invalid image format",
                "detail": "The provided image must be in JPEG, PNG, or WebP format",
                "code": "INVALID_IMAGE_FORMAT",
                "request_id": "req_123456789"
            }
        }
