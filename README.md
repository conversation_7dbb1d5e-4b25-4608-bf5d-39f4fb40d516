# SnapCode - AI-Powered UI Screenshot to Code Converter

SnapCode is an advanced tool that converts UI screenshots into clean, functional code using computer vision and machine learning. It supports multiple frameworks and provides accurate, maintainable code output.

## Features

- **Intelligent UI Element Detection**: Uses advanced computer vision to identify UI components
- **Multi-Framework Support**: Generates code for HTML/CSS, React, Vue, and SwiftUI
- **Layout Analysis**: Accurately preserves spatial relationships and hierarchy
- **Style Extraction**: Automatically detects and applies consistent styling
- **Code Optimization**: Generates clean, maintainable code following best practices
- **Real-time Preview**: Instant visualization of the generated code

## System Architecture

```mermaid
flowchart TB
    subgraph Input
        A[Screenshot Upload] --> B[Image Preprocessing]
    end
    
    subgraph Recognition
        B --> C[UI Element Detection]
        C --> D[Layout Analysis]
        C --> E[Style Extraction]
    end
    
    subgraph Generation
        D --> F[Component Generation]
        E --> F
        F --> G[Code Optimization]
        G --> H[Framework-Specific Output]
    end
    
    subgraph Output
        H --> I[Preview]
        H --> J[Code Export]
    end
```

## Project Structure

```
SnapCode/
├── backend/
│   ├── app/
│   │   ├── recognition/
│   │   │   ├── detector.py      # UI element detection
│   │   │   └── layout.py        # Layout analysis
│   │   ├── codegen/
│   │   │   ├── html.py          # HTML code generation
│   │   │   ├── react.py         # React component generation
│   │   │   └── swift.py         # SwiftUI code generation
│   │   └── utils/
│   │       ├── image.py         # Image processing utilities
│   │       └── style.py         # Style extraction
│   └── tests/
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   └── styles/
│   └── public/
└── docs/
```

## Installation

1. Clone the repository:
```bash
git clone https://github.com/HectorTa1989/snapcode.git
cd snapcode
```

2. Install backend dependencies:
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

3. Install frontend dependencies:
```bash
cd frontend
npm install
```

## Usage

1. Start the backend server:
```bash
cd backend
python main.py
```

2. Start the frontend development server:
```bash
cd frontend
npm run dev
```

3. Open your browser and navigate to `http://localhost:3000`

## API Documentation

### POST /api/convert
Converts a screenshot into code.

**Request Body:**
```json
{
    "image": "base64_encoded_image",
    "framework": "react",
    "options": {
        "styling": "tailwind",
        "typescript": true
    }
}
```

**Response:**
```json
{
    "code": "generated_code",
    "components": [...],
    "styles": {...}
}
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- OpenCV for computer vision capabilities
- TensorFlow for machine learning models
- React and FastAPI for the web application