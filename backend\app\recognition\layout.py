from typing import List, Optional
from .detector import UIElement

class LayoutAnalyzer:
    """Analyzes spatial relationships and hierarchy among UI elements."""

    def __init__(self):
        self.min_overlap = 0.5  # Minimum overlap ratio to consider containment
        self.spacing_threshold = 10  # Pixels threshold for alignment

    def analyze(self, elements: List[UIElement]) -> UIElement:
        """Analyzes layout structure and returns a root container with hierarchy."""
        if not elements:
            return None

        # Create root container
        max_x = max(el.bbox[0] + el.bbox[2] for el in elements)
        max_y = max(el.bbox[1] + el.bbox[3] for el in elements)
        root = UIElement('container', (0, 0, max_x, max_y))

        # Sort elements by area (largest first) for container detection
        sorted_elements = sorted(
            elements,
            key=lambda el: el.bbox[2] * el.bbox[3],
            reverse=True
        )

        # Build element hierarchy
        remaining = self._build_hierarchy(sorted_elements)
        root.children = remaining

        # Analyze alignment and spacing
        self._analyze_alignment(root)
        
        return root

    def _build_hierarchy(self, elements: List[UIElement]) -> List[UIElement]:
        """Builds parent-child relationships between elements."""
        remaining = elements.copy()
        
        for i, parent in enumerate(elements):
            if parent not in remaining:
                continue
                
            for child in elements[i+1:]:
                if child not in remaining:
                    continue
                    
                if self._is_container(parent, child):
                    parent.children.append(child)
                    remaining.remove(child)
        
        return remaining

    def _is_container(self, parent: UIElement, child: UIElement) -> bool:
        """Determines if one element contains another based on overlap ratio."""
        px, py, pw, ph = parent.bbox
        cx, cy, cw, ch = child.bbox
        
        # Check if child is completely inside parent
        if (cx >= px and cy >= py and 
            cx + cw <= px + pw and cy + ch <= py + ph):
            return True
            
        # Calculate overlap area
        x_overlap = max(0, min(px + pw, cx + cw) - max(px, cx))
        y_overlap = max(0, min(py + ph, cy + ch) - max(py, cy))
        overlap_area = x_overlap * y_overlap
        child_area = cw * ch
        
        return overlap_area / child_area >= self.min_overlap

    def _analyze_alignment(self, root: UIElement):
        """Analyzes alignment patterns among sibling elements."""
        def analyze_siblings(elements: List[UIElement]):
            if not elements:
                return
                
            # Check horizontal alignment
            for i, el1 in enumerate(elements):
                for el2 in elements[i+1:]:
                    if abs(el1.bbox[1] - el2.bbox[1]) <= self.spacing_threshold:
                        el1.style['align'] = el2.style['align'] = 'horizontal'
                        
            # Check vertical alignment
            for i, el1 in enumerate(elements):
                for el2 in elements[i+1:]:
                    if abs(el1.bbox[0] - el2.bbox[0]) <= self.spacing_threshold:
                        el1.style['align'] = el2.style['align'] = 'vertical'
                        
            # Recursively analyze children
            for element in elements:
                analyze_siblings(element.children)
        
        analyze_siblings(root.children)