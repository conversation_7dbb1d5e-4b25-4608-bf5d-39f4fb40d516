"""
SnapCode FastAPI Application

This module defines the FastAPI application with all routes, middleware,
and configuration for the SnapCode screenshot-to-code conversion service.
"""

import time
import uuid
import logging
from datetime import datetime
from typing import Dict, Any, Optional
import traceback

from fastapi import FastAPI, HTTPException, Request, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

from .config import settings
from .models import (
    ConversionRequest, ConversionResponse, ConversionResult,
    HealthCheckResponse, ErrorResponse, FrameworkType
)
from .recognition.detector import UIElementDetector
from .recognition.layout import LayoutAnalyzer
from .codegen.html import HTMLCodeGenerator
from .codegen.react import ReactCodeGenerator
from .codegen.swift import SwiftUICodeGenerator
from .utils.image import ImageProcessor

# Configure logging
logging.basicConfig(level=getattr(logging, settings.log_level))
logger = logging.getLogger(__name__)

# Initialize rate limiter
limiter = Limiter(key_func=get_remote_address)

# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    description=settings.app_description,
    version=settings.app_version,
    docs_url=settings.docs_url if settings.debug else None,
    redoc_url=settings.redoc_url if settings.debug else None,
    openapi_url=settings.openapi_url if settings.debug else None,
)

# Add rate limiting error handler
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=settings.cors_allow_credentials,
    allow_methods=settings.cors_allow_methods,
    allow_headers=settings.cors_allow_headers,
)

# Add trusted host middleware for production
if settings.environment.value == "production":
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"]  # Configure with actual allowed hosts in production
    )

# Application startup time for uptime calculation
startup_time = time.time()

# Initialize processors
image_processor = ImageProcessor()
ui_detector = UIElementDetector()
layout_analyzer = LayoutAnalyzer()

# Initialize code generators
html_generator = HTMLCodeGenerator()
react_generator = ReactCodeGenerator()
swift_generator = SwiftUICodeGenerator()

@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """Add processing time header to responses."""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response

@app.middleware("http")
async def add_request_id_header(request: Request, call_next):
    """Add unique request ID to responses."""
    request_id = str(uuid.uuid4())
    request.state.request_id = request_id
    response = await call_next(request)
    response.headers["X-Request-ID"] = request_id
    return response

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler for unhandled errors."""
    request_id = getattr(request.state, 'request_id', 'unknown')
    
    logger.error(f"Unhandled exception for request {request_id}: {str(exc)}")
    logger.error(traceback.format_exc())
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": "Internal server error",
            "detail": "An unexpected error occurred while processing your request",
            "request_id": request_id
        }
    )

@app.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """Health check endpoint."""
    uptime = time.time() - startup_time
    
    return HealthCheckResponse(
        status="healthy",
        version=settings.app_version,
        timestamp=datetime.utcnow().isoformat() + "Z",
        uptime=uptime
    )

@app.get("/")
async def root():
    """Root endpoint with basic information."""
    return {
        "name": settings.app_name,
        "version": settings.app_version,
        "description": settings.app_description,
        "docs_url": settings.docs_url if settings.debug else None,
        "health_url": "/health"
    }

@app.post(f"{settings.api_prefix}/convert", response_model=ConversionResponse)
@limiter.limit(f"{settings.rate_limit_requests}/{settings.rate_limit_window}seconds")
async def convert_screenshot(request: Request, conversion_request: ConversionRequest):
    """
    Convert a UI screenshot to code.
    
    This endpoint processes a base64-encoded screenshot image and generates
    code in the specified framework (HTML, React, Vue, or SwiftUI).
    """
    request_id = getattr(request.state, 'request_id', str(uuid.uuid4()))
    
    try:
        logger.info(f"Starting conversion request {request_id} for framework: {conversion_request.framework}")
        
        # Process the image
        logger.debug(f"Processing image for request {request_id}")
        image_array = image_processor.decode_base64_image(conversion_request.image)
        processed_image = image_processor.preprocess_for_detection(image_array)
        
        # Detect UI elements
        logger.debug(f"Detecting UI elements for request {request_id}")
        elements = ui_detector.detect_elements(processed_image)
        
        if not elements:
            logger.warning(f"No UI elements detected for request {request_id}")
            return ConversionResponse(
                success=False,
                error="No UI elements detected in the image",
                request_id=request_id
            )
        
        # Analyze layout
        logger.debug(f"Analyzing layout for request {request_id}")
        root_element = layout_analyzer.analyze(elements)
        
        # Generate code based on framework
        logger.debug(f"Generating {conversion_request.framework} code for request {request_id}")
        
        if conversion_request.framework == FrameworkType.HTML:
            result = html_generator.generate(root_element)
            code = result['html']
            
        elif conversion_request.framework == FrameworkType.REACT:
            result = react_generator.generate(
                root_element, 
                {
                    'styling': conversion_request.options.styling,
                    'typescript': conversion_request.options.typescript,
                    'responsive': conversion_request.options.responsive,
                    'accessibility': conversion_request.options.accessibility
                }
            )
            code = result['code']
            
        elif conversion_request.framework == FrameworkType.SWIFT:
            result = swift_generator.generate(root_element)
            code = result['code']
            
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported framework: {conversion_request.framework}"
            )
        
        # Prepare response
        conversion_result = ConversionResult(
            code=code,
            framework=conversion_request.framework,
            components=[
                {
                    'type': elem.type,
                    'bbox': list(elem.bbox),
                    'text': elem.text,
                    'style': elem.style,
                    'confidence': elem.confidence,
                    'children': []  # Simplified for response
                }
                for elem in elements
            ],
            styles={},  # Could be populated with extracted styles
            metadata={
                'processing_time': time.time() - startup_time,
                'elements_detected': len(elements),
                'framework_used': conversion_request.framework,
                'options_used': conversion_request.options.dict()
            }
        )
        
        logger.info(f"Successfully completed conversion request {request_id}")
        
        return ConversionResponse(
            success=True,
            result=conversion_result,
            request_id=request_id
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
        
    except Exception as e:
        logger.error(f"Conversion failed for request {request_id}: {str(e)}")
        logger.error(traceback.format_exc())
        
        return ConversionResponse(
            success=False,
            error=f"Conversion failed: {str(e)}",
            request_id=request_id
        )

@app.get(f"{settings.api_prefix}/frameworks")
async def get_supported_frameworks():
    """Get list of supported frameworks."""
    return {
        "frameworks": [framework.value for framework in FrameworkType],
        "default": "react"
    }

@app.get(f"{settings.api_prefix}/status")
async def get_api_status():
    """Get API status and configuration."""
    return {
        "status": "operational",
        "version": settings.app_version,
        "environment": settings.environment.value,
        "rate_limit": {
            "requests": settings.rate_limit_requests,
            "window_seconds": settings.rate_limit_window,
            "enabled": settings.rate_limit_enabled
        },
        "supported_frameworks": [framework.value for framework in FrameworkType],
        "max_file_size_mb": settings.max_file_size / (1024 * 1024),
        "allowed_image_types": settings.allowed_image_types
    }

# Development-only endpoints
if settings.debug:
    @app.get("/debug/test-detection")
    async def test_detection():
        """Test endpoint for UI detection (development only)."""
        return {
            "message": "Detection test endpoint",
            "detector_initialized": ui_detector is not None,
            "layout_analyzer_initialized": layout_analyzer is not None
        }
    
    @app.get("/debug/test-generation")
    async def test_generation():
        """Test endpoint for code generation (development only)."""
        return {
            "message": "Generation test endpoint",
            "generators": {
                "html": html_generator is not None,
                "react": react_generator is not None,
                "swift": swift_generator is not None
            }
        }
