"""
SnapCode React Code Generator

This module generates React components from UI elements with TypeScript support,
modern React patterns, and responsive design principles.
"""

import logging
from typing import List, Dict, Optional, Any
from functools import lru_cache
from ..recognition.detector import UIElement
from ..utils.style import StyleExtractor

class ReactGenerationError(Exception):
    """Custom exception for React code generation errors."""
    pass

class ReactCodeGenerator:
    """Generates React components from UI elements with modern patterns."""
    
    SUPPORTED_STYLING = ['tailwind', 'styled-components', 'css-modules', 'vanilla']
    
    def __init__(self, styling: str = 'tailwind', typescript: bool = True, cache_size: int = 128):
        """
        Initialize the React code generator.
        
        Args:
            styling: Styling approach ('tailwind', 'styled-components', etc.)
            typescript: Whether to generate TypeScript code
            cache_size: Size of the LRU cache for component mappings
        """
        if styling not in self.SUPPORTED_STYLING:
            raise ValueError(f"Unsupported styling: {styling}. Must be one of {self.SUPPORTED_STYLING}")
        
        self.styling = styling
        self.typescript = typescript
        self.indent = '  '
        self.logger = logging.getLogger(__name__)
        self.style_extractor = StyleExtractor()
        self.component_counter = 0
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup logging configuration."""
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def generate(self, root: UIElement, options: Dict[str, Any] = None) -> Dict[str, str]:
        """
        Generate React component code from UI element hierarchy.
        
        Args:
            root: Root UI element
            options: Generation options
            
        Returns:
            Dictionary containing generated code and metadata
        """
        try:
            if not root:
                raise ReactGenerationError("Root element is required")
            
            options = options or {}
            self.component_counter = 0
            
            self.logger.info(f"Starting React generation with styling: {self.styling}")
            
            # Generate component code
            component_code = self._generate_component(root, 'GeneratedComponent', options)
            
            # Generate imports
            imports = self._generate_imports(options)
            
            # Generate exports
            exports = self._generate_exports('GeneratedComponent')
            
            # Combine all parts
            full_code = '\n'.join([
                imports,
                '',
                component_code,
                '',
                exports
            ])
            
            return {
                'code': full_code,
                'language': 'typescript' if self.typescript else 'javascript',
                'framework': 'react',
                'styling': self.styling,
                'components_generated': self.component_counter
            }
            
        except Exception as e:
            self.logger.error(f"React generation failed: {str(e)}")
            raise ReactGenerationError(f"Generation failed: {str(e)}")
    
    def _generate_imports(self, options: Dict[str, Any]) -> str:
        """Generate import statements."""
        imports = []
        
        # React imports
        if self.typescript:
            imports.append("import React from 'react';")
        else:
            imports.append("import React from 'react';")
        
        # Styling imports
        if self.styling == 'styled-components':
            imports.append("import styled from 'styled-components';")
        elif self.styling == 'css-modules':
            imports.append("import styles from './GeneratedComponent.module.css';")
        
        # Additional imports based on options
        if options.get('use_hooks', False):
            imports.append("import { useState, useEffect } from 'react';")
        
        return '\n'.join(imports)
    
    def _generate_exports(self, component_name: str) -> str:
        """Generate export statement."""
        return f"export default {component_name};"
    
    def _generate_component(self, element: UIElement, name: str, options: Dict[str, Any]) -> str:
        """Generate a React component from a UI element."""
        self.component_counter += 1
        
        # Generate component signature
        if self.typescript:
            signature = f"const {name}: React.FC = () => {{"
        else:
            signature = f"const {name} = () => {{"
        
        # Generate component body
        body_lines = []
        body_lines.append(signature)
        body_lines.append(f"{self.indent}return (")
        
        # Generate JSX
        jsx_lines = self._generate_jsx_element(element, 2, options)
        body_lines.extend(jsx_lines)
        
        body_lines.append(f"{self.indent});")
        body_lines.append("};")
        
        return '\n'.join(body_lines)
    
    def _generate_jsx_element(self, element: UIElement, depth: int, options: Dict[str, Any]) -> List[str]:
        """Generate JSX for a UI element and its children."""
        if not element:
            return []
        
        lines = []
        indent = self.indent * depth
        
        # Determine HTML tag
        tag = self._get_html_tag(element)
        
        # Generate opening tag with attributes
        attributes = self._generate_attributes(element, options)
        if attributes:
            opening_tag = f"{indent}<{tag} {attributes}>"
        else:
            opening_tag = f"{indent}<{tag}>"
        
        # Handle self-closing tags
        if tag in ['img', 'input', 'br', 'hr']:
            if attributes:
                lines.append(f"{indent}<{tag} {attributes} />")
            else:
                lines.append(f"{indent}<{tag} />")
            return lines
        
        lines.append(opening_tag)
        
        # Add text content
        if element.text and element.text.strip():
            text_content = self._escape_jsx_text(element.text.strip())
            lines.append(f"{indent}{self.indent}{text_content}")
        
        # Add children
        for child in element.children:
            child_lines = self._generate_jsx_element(child, depth + 1, options)
            lines.extend(child_lines)
        
        # Closing tag
        lines.append(f"{indent}</{tag}>")
        
        return lines
    
    def _get_html_tag(self, element: UIElement) -> str:
        """Determine the appropriate HTML tag for a UI element."""
        element_type = element.type.lower()
        
        tag_mapping = {
            'button': 'button',
            'input': 'input',
            'text': 'p',
            'heading': 'h2',
            'image': 'img',
            'container': 'div',
            'list': 'ul',
            'listitem': 'li',
            'link': 'a',
            'form': 'form',
            'label': 'label',
            'textarea': 'textarea',
            'select': 'select',
            'option': 'option',
            'nav': 'nav',
            'header': 'header',
            'footer': 'footer',
            'section': 'section',
            'article': 'article',
            'aside': 'aside'
        }
        
        return tag_mapping.get(element_type, 'div')
    
    def _generate_attributes(self, element: UIElement, options: Dict[str, Any]) -> str:
        """Generate HTML attributes for an element."""
        attributes = []
        
        # Generate className
        class_name = self._generate_class_name(element, options)
        if class_name:
            attributes.append(f'className="{class_name}"')
        
        # Generate style attribute for inline styles
        if self.styling == 'vanilla':
            inline_style = self._generate_inline_style(element)
            if inline_style:
                attributes.append(f'style={{{inline_style}}}')
        
        # Element-specific attributes
        element_type = element.type.lower()
        
        if element_type == 'button':
            attributes.append('type="button"')
            if options.get('add_click_handlers', True):
                attributes.append('onClick={() => console.log("Button clicked")}')
        
        elif element_type == 'input':
            attributes.append('type="text"')
            if element.text:
                attributes.append(f'placeholder="{self._escape_attribute(element.text)}"')
        
        elif element_type == 'image':
            attributes.append('src="/placeholder-image.jpg"')
            attributes.append('alt="Generated image"')
        
        elif element_type == 'link':
            attributes.append('href="#"')
        
        # Accessibility attributes
        if options.get('accessibility', True):
            if element_type == 'button':
                attributes.append('role="button"')
            elif element_type == 'image' and not any('alt=' in attr for attr in attributes):
                attributes.append('alt=""')
        
        return ' '.join(attributes)
    
    def _generate_class_name(self, element: UIElement, options: Dict[str, Any]) -> str:
        """Generate CSS class names for an element."""
        if self.styling == 'tailwind':
            return self._generate_tailwind_classes(element, options)
        elif self.styling == 'css-modules':
            return self._generate_css_module_classes(element)
        elif self.styling == 'styled-components':
            return ''  # Styled components don't use className
        else:
            return self._generate_vanilla_classes(element)
    
    def _generate_tailwind_classes(self, element: UIElement, options: Dict[str, Any]) -> str:
        """Generate Tailwind CSS classes."""
        classes = []
        
        # Base classes based on element type
        element_type = element.type.lower()
        
        if element_type == 'button':
            classes.extend([
                'px-4', 'py-2', 'bg-blue-500', 'text-white', 
                'rounded', 'hover:bg-blue-600', 'transition-colors',
                'focus:outline-none', 'focus:ring-2', 'focus:ring-blue-500'
            ])
        
        elif element_type == 'input':
            classes.extend([
                'px-3', 'py-2', 'border', 'border-gray-300', 'rounded',
                'focus:outline-none', 'focus:ring-2', 'focus:ring-blue-500',
                'focus:border-transparent'
            ])
        
        elif element_type == 'container':
            classes.extend(['flex', 'flex-col', 'space-y-4', 'p-4'])
        
        elif element_type == 'text':
            classes.extend(['text-gray-800', 'leading-relaxed'])
        
        # Responsive classes
        if options.get('responsive', True):
            if element_type == 'container':
                classes.extend(['sm:p-6', 'md:p-8', 'lg:p-10'])
        
        # Layout classes based on element style
        if element.style.get('align') == 'horizontal':
            classes.extend(['flex', 'items-center', 'space-x-4'])
        elif element.style.get('align') == 'vertical':
            classes.extend(['flex', 'flex-col', 'space-y-4'])
        
        return ' '.join(classes)
    
    def _generate_css_module_classes(self, element: UIElement) -> str:
        """Generate CSS module class names."""
        element_type = element.type.lower()
        return f"styles.{element_type}"
    
    def _generate_vanilla_classes(self, element: UIElement) -> str:
        """Generate vanilla CSS class names."""
        element_type = element.type.lower()
        return f"generated-{element_type}"
    
    def _generate_inline_style(self, element: UIElement) -> str:
        """Generate inline style object for React."""
        if not element.style:
            return ''
        
        style_props = []
        
        for key, value in element.style.items():
            # Convert CSS property names to camelCase
            camel_key = self._to_camel_case(key)
            
            # Format value appropriately
            if isinstance(value, str):
                style_props.append(f"{camel_key}: '{value}'")
            else:
                style_props.append(f"{camel_key}: {value}")
        
        return '{' + ', '.join(style_props) + '}'
    
    def _to_camel_case(self, snake_str: str) -> str:
        """Convert snake_case or kebab-case to camelCase."""
        components = snake_str.replace('-', '_').split('_')
        return components[0] + ''.join(word.capitalize() for word in components[1:])
    
    def _escape_jsx_text(self, text: str) -> str:
        """Escape text content for JSX."""
        return text.replace('{', '{{').replace('}', '}}')
    
    def _escape_attribute(self, text: str) -> str:
        """Escape attribute values."""
        return text.replace('"', '&quot;').replace("'", '&#39;')
