"""
SnapCode Image Processing Utilities

This module provides utilities for image preprocessing, format conversion,
base64 handling, and other image-related operations for the SnapCode application.
"""

import base64
import io
import logging
from typing import Tuple, Optional, Union
import numpy as np
import cv2
from PIL import Image, ImageEnhance, ImageFilter
from ..config import settings

logger = logging.getLogger(__name__)

class ImageProcessingError(Exception):
    """Custom exception for image processing errors."""
    pass

class ImageProcessor:
    """Handles image processing operations for UI screenshot analysis."""
    
    SUPPORTED_FORMATS = ['JPEG', 'PNG', 'WebP', 'BMP']
    MAX_DIMENSION = 2048  # Maximum width or height
    MIN_DIMENSION = 100   # Minimum width or height
    
    def __init__(self):
        """Initialize the image processor."""
        self.logger = logging.getLogger(__name__)
    
    def decode_base64_image(self, base64_data: str) -> np.ndarray:
        """
        Decode base64 image data to numpy array.
        
        Args:
            base64_data: Base64 encoded image string
            
        Returns:
            numpy.ndarray: Decoded image as numpy array
            
        Raises:
            ImageProcessingError: If decoding fails
        """
        try:
            # Remove data URL prefix if present
            if base64_data.startswith('data:'):
                base64_data = base64_data.split(',', 1)[1]
            
            # Decode base64 to bytes
            image_bytes = base64.b64decode(base64_data)
            
            # Validate file size
            if len(image_bytes) > settings.max_file_size:
                raise ImageProcessingError(f"Image size exceeds maximum allowed size of {settings.max_file_size} bytes")
            
            # Convert to PIL Image
            pil_image = Image.open(io.BytesIO(image_bytes))
            
            # Validate image format
            if pil_image.format not in self.SUPPORTED_FORMATS:
                raise ImageProcessingError(f"Unsupported image format: {pil_image.format}")
            
            # Convert to RGB if necessary
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            
            # Convert to numpy array
            image_array = np.array(pil_image)
            
            # Convert RGB to BGR for OpenCV
            image_bgr = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
            
            self.logger.info(f"Successfully decoded image: {image_bgr.shape}")
            return image_bgr
            
        except Exception as e:
            self.logger.error(f"Failed to decode base64 image: {str(e)}")
            raise ImageProcessingError(f"Failed to decode image: {str(e)}")
    
    def encode_image_to_base64(self, image: np.ndarray, format: str = 'PNG') -> str:
        """
        Encode numpy array image to base64 string.
        
        Args:
            image: Image as numpy array
            format: Output format (PNG, JPEG, etc.)
            
        Returns:
            str: Base64 encoded image string
            
        Raises:
            ImageProcessingError: If encoding fails
        """
        try:
            # Convert BGR to RGB for PIL
            if len(image.shape) == 3:
                image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            else:
                image_rgb = image
            
            # Convert to PIL Image
            pil_image = Image.fromarray(image_rgb)
            
            # Save to bytes buffer
            buffer = io.BytesIO()
            pil_image.save(buffer, format=format)
            buffer.seek(0)
            
            # Encode to base64
            base64_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            return base64_data
            
        except Exception as e:
            self.logger.error(f"Failed to encode image to base64: {str(e)}")
            raise ImageProcessingError(f"Failed to encode image: {str(e)}")
    
    def validate_and_resize(self, image: np.ndarray) -> np.ndarray:
        """
        Validate image dimensions and resize if necessary.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            numpy.ndarray: Validated and potentially resized image
            
        Raises:
            ImageProcessingError: If image is invalid
        """
        try:
            height, width = image.shape[:2]
            
            # Check minimum dimensions
            if width < self.MIN_DIMENSION or height < self.MIN_DIMENSION:
                raise ImageProcessingError(f"Image too small: {width}x{height}. Minimum size: {self.MIN_DIMENSION}x{self.MIN_DIMENSION}")
            
            # Resize if too large
            if width > self.MAX_DIMENSION or height > self.MAX_DIMENSION:
                self.logger.info(f"Resizing image from {width}x{height}")
                
                # Calculate new dimensions maintaining aspect ratio
                scale = min(self.MAX_DIMENSION / width, self.MAX_DIMENSION / height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                
                # Resize image
                image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
                self.logger.info(f"Resized image to {new_width}x{new_height}")
            
            return image
            
        except Exception as e:
            self.logger.error(f"Failed to validate and resize image: {str(e)}")
            raise ImageProcessingError(f"Image validation failed: {str(e)}")
    
    def enhance_image(self, image: np.ndarray) -> np.ndarray:
        """
        Enhance image quality for better UI element detection.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            numpy.ndarray: Enhanced image
        """
        try:
            # Convert to PIL for enhancement
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(image_rgb)
            
            # Enhance contrast
            enhancer = ImageEnhance.Contrast(pil_image)
            pil_image = enhancer.enhance(1.2)
            
            # Enhance sharpness
            enhancer = ImageEnhance.Sharpness(pil_image)
            pil_image = enhancer.enhance(1.1)
            
            # Apply slight blur to reduce noise
            pil_image = pil_image.filter(ImageFilter.GaussianBlur(radius=0.5))
            
            # Convert back to numpy array
            enhanced_rgb = np.array(pil_image)
            enhanced_bgr = cv2.cvtColor(enhanced_rgb, cv2.COLOR_RGB2BGR)
            
            self.logger.debug("Image enhancement completed")
            return enhanced_bgr
            
        except Exception as e:
            self.logger.warning(f"Image enhancement failed, using original: {str(e)}")
            return image
    
    def preprocess_for_detection(self, image: np.ndarray) -> np.ndarray:
        """
        Preprocess image specifically for UI element detection.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            numpy.ndarray: Preprocessed image optimized for detection
        """
        try:
            # Validate and resize
            processed = self.validate_and_resize(image)
            
            # Enhance image quality
            processed = self.enhance_image(processed)
            
            # Additional preprocessing for detection
            # Convert to grayscale for edge detection
            gray = cv2.cvtColor(processed, cv2.COLOR_BGR2GRAY)
            
            # Apply adaptive histogram equalization
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            gray = clahe.apply(gray)
            
            # Convert back to BGR
            processed = cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)
            
            self.logger.info("Image preprocessing for detection completed")
            return processed
            
        except Exception as e:
            self.logger.error(f"Image preprocessing failed: {str(e)}")
            raise ImageProcessingError(f"Preprocessing failed: {str(e)}")
    
    def get_image_info(self, image: np.ndarray) -> dict:
        """
        Get information about the image.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            dict: Image information including dimensions, channels, etc.
        """
        height, width = image.shape[:2]
        channels = image.shape[2] if len(image.shape) > 2 else 1
        
        return {
            'width': width,
            'height': height,
            'channels': channels,
            'dtype': str(image.dtype),
            'size_bytes': image.nbytes,
            'aspect_ratio': width / height if height > 0 else 0
        }
