# Deployment Guide

## Local Testing

### Prerequisites
- Python 3.9 or higher
- pip (Python package manager)
- Docker (optional, for containerized testing)

### Setup and Testing

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run Tests**
   ```bash
   pytest
   ```

3. **Run with Docker (Optional)**
   ```bash
   docker build -t snapcode .
   docker run -p 8000:8000 snapcode
   ```

## Free Hosting Options

### Heroku Deployment

1. **Install Heroku CLI**
   - Download and install from [Heroku CLI](https://devcenter.heroku.com/articles/heroku-cli)

2. **Login to Heroku**
   ```bash
   heroku login
   ```

3. **Create Heroku App**
   ```bash
   heroku create your-app-name
   ```

4. **Deploy**
   ```bash
   git push heroku main
   ```

### Vercel Deployment

1. **Install Vercel CLI**
   ```bash
   npm i -g vercel
   ```

2. **Deploy**
   ```bash
   vercel
   ```

### Railway.app Deployment

1. **Visit [Railway.app](https://railway.app)**
2. **Connect your GitHub repository**
3. **Select your repository and deploy**

## Environment Variables

Create a `.env` file for local development:
```env
DEBUG=False
LOG_LEVEL=INFO
PORT=8000
```

Set these variables in your hosting platform's environment settings.

## Health Checks

The application provides a health check endpoint at `/health` that returns:
```json
{
    "status": "healthy",
    "version": "1.0.0"
}
```

## Monitoring

- Use the built-in logging system
- Monitor application logs through your hosting platform's dashboard
- Set up alerts for error rates and response times

## Troubleshooting

1. **Application won't start**
   - Check if all dependencies are installed
   - Verify environment variables
   - Check the logs for specific errors

2. **Tests failing**
   - Run tests locally with `-v` flag for verbose output
   - Check if all dependencies are up to date
   - Verify test environment variables

3. **Deployment failing**
   - Verify git repository is clean and up to date
   - Check deployment logs
   - Verify platform-specific requirements