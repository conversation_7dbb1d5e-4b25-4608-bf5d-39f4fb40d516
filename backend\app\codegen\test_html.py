import pytest
from typing import Dict
from ..recognition.detector import UIElement
from .html import <PERSON><PERSON><PERSON>odeGenerator, HTMLGenerationError, InvalidElementError

@pytest.fixture
def html_generator():
    return HTMLCodeGenerator(framework='tailwind')

@pytest.fixture
def mock_element():
    element = UIElement()
    element.type = 'button'
    element.bbox = [0, 0, 100, 50]
    element.text = 'Test Button'
    element.style = {'align': 'horizontal'}
    element.children = []
    return element

def test_initialization():
    # Test valid framework initialization
    generator = HTMLCodeGenerator(framework='tailwind')
    assert generator.framework == 'tailwind'
    
    # Test invalid framework initialization
    with pytest.raises(ValueError):
        HTMLCodeGenerator(framework='invalid')

def test_validate_element(html_generator, mock_element):
    # Test valid element
    html_generator._validate_element(mock_element)
    
    # Test None element
    with pytest.raises(InvalidElementError):
        html_generator._validate_element(None)
    
    # Test invalid element type
    invalid_element = mock_element
    invalid_element.type = None
    with pytest.raises(InvalidElementError):
        html_generator._validate_element(invalid_element)

def test_generate_html(html_generator, mock_element):
    # Test basic HTML generation
    result = html_generator.generate(mock_element)
    assert isinstance(result, Dict)
    assert 'html' in result
    assert 'css' in result
    
    # Verify HTML structure
    html = result['html']
    assert '<!DOCTYPE html>' in html
    assert '<html lang="en">' in html
    assert '<button' in html
    assert 'Test Button' in html
    assert '</html>' in html

def test_get_html_tag(html_generator):
    # Test known element types
    element = mock_element()
    element.type = 'button'
    assert html_generator._get_html_tag(element) == 'button'
    
    element.type = 'input'
    assert html_generator._get_html_tag(element) == 'input'
    
    # Test unknown element type
    element.type = 'unknown'
    assert html_generator._get_html_tag(element) == 'div'

def test_get_element_classes(html_generator):
    # Test class generation for different element types
    classes = html_generator._get_element_classes('button', 100, 50, 'horizontal')
    assert isinstance(classes, str)
    assert 'px-4' in classes  # Verify Tailwind classes are included
    
    # Test caching
    cached_classes = html_generator._get_element_classes('button', 100, 50, 'horizontal')
    assert classes == cached_classes

def test_get_inline_styles(html_generator, mock_element):
    # Test style generation
    mock_element.style['background-color'] = '#ff0000'
    mock_element.style['min-width'] = '100'
    
    styles = html_generator._get_inline_styles(mock_element)
    assert 'background-color: #ff0000' in styles
    assert 'min-width: 100px' in styles
    
    # Test invalid color handling
    mock_element.style['color'] = 'invalid-color'
    styles = html_generator._get_inline_styles(mock_element)
    assert 'color: invalid-color' not in styles

def test_is_valid_color(html_generator):
    # Test hex colors
    assert html_generator._is_valid_color('#fff')
    assert html_generator._is_valid_color('#ffffff')
    assert not html_generator._is_valid_color('#ffff')
    
    # Test rgb/rgba colors
    assert html_generator._is_valid_color('rgb(255, 255, 255)')
    assert html_generator._is_valid_color('rgba(255, 255, 255, 0.5)')
    
    # Test named colors
    assert html_generator._is_valid_color('white')
    assert not html_generator._is_valid_color('invalid-color')