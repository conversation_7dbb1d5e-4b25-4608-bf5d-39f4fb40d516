#!/usr/bin/env python3
"""
SnapCode - AI-Powered UI Screenshot to Code Converter
Main application entry point for the FastAPI backend server.

This module initializes and runs the FastAPI application with all necessary
middleware, routes, and configuration for converting UI screenshots to code.
"""

import os
import sys
import uvicorn
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_dir))

from backend.app.config import settings
from backend.app.api import app

def main():
    """Main entry point for the SnapCode application."""
    print("🚀 Starting SnapCode - AI-Powered UI Screenshot to Code Converter")
    print(f"📍 Environment: {settings.environment}")
    print(f"🔧 Debug mode: {settings.debug}")
    print(f"🌐 Server will run on: http://{settings.host}:{settings.port}")
    
    # Configure uvicorn server
    uvicorn.run(
        "backend.app.api:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=True,
        reload_dirs=["backend/app"] if settings.debug else None,
    )

if __name__ == "__main__":
    main()
