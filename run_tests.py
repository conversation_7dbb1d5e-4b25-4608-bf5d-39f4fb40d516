#!/usr/bin/env python3
"""
SnapCode Test Runner

This script runs the comprehensive test suite for SnapCode including
unit tests, integration tests, and API endpoint tests.
"""

import sys
import subprocess
import argparse
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}")
    print(f"Running: {' '.join(command)}")
    
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"Error: {e}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False

def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="Run SnapCode tests")
    parser.add_argument("--coverage", action="store_true", help="Run tests with coverage")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--fast", action="store_true", help="Skip slow tests")
    parser.add_argument("--module", "-m", help="Run tests for specific module")
    
    args = parser.parse_args()
    
    print("🧪 SnapCode Test Suite Runner")
    print("=" * 50)
    
    # Check if pytest is installed
    try:
        import pytest
        print(f"✅ pytest version: {pytest.__version__}")
    except ImportError:
        print("❌ pytest not found. Installing...")
        if not run_command([sys.executable, "-m", "pip", "install", "pytest", "pytest-asyncio", "pytest-cov"], "Installing pytest"):
            return 1
    
    # Build pytest command
    pytest_cmd = [sys.executable, "-m", "pytest"]
    
    # Add test directory
    if args.module:
        test_path = f"backend/app/tests/test_{args.module}.py"
        if not Path(test_path).exists():
            print(f"❌ Test module not found: {test_path}")
            return 1
        pytest_cmd.append(test_path)
    else:
        pytest_cmd.append("backend/app/tests/")
    
    # Add options
    if args.verbose:
        pytest_cmd.append("-v")
    
    if args.fast:
        pytest_cmd.extend(["-m", "not slow"])
    
    if args.coverage:
        pytest_cmd.extend([
            "--cov=backend/app",
            "--cov-report=html",
            "--cov-report=term-missing",
            "--cov-fail-under=80"
        ])
    
    # Add additional pytest options
    pytest_cmd.extend([
        "--tb=short",
        "--strict-markers",
        "--disable-warnings"
    ])
    
    # Run tests
    success = run_command(pytest_cmd, "Running tests")
    
    if success:
        print("\n🎉 All tests passed!")
        if args.coverage:
            print("📊 Coverage report generated in htmlcov/")
    else:
        print("\n💥 Some tests failed!")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
