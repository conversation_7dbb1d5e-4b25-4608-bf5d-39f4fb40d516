#!/usr/bin/env python3
"""
SnapCode Backend Application Module Entry Point

This module allows the backend application to be run as a module:
python -m backend.app

It initializes the FastAPI application and starts the uvicorn server
with appropriate configuration for development and production environments.
"""

import uvicorn
from .config import settings
from .api import app

def main():
    """Main entry point when running as a module."""
    print("🚀 Starting SnapCode Backend Application")
    print(f"📍 Environment: {settings.environment}")
    print(f"🔧 Debug mode: {settings.debug}")
    print(f"🌐 Server running on: http://{settings.host}:{settings.port}")
    
    # Run the FastAPI application with uvicorn
    uvicorn.run(
        "backend.app.api:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=True,
    )

if __name__ == "__main__":
    main()
