"""
Unit tests for code generation modules.
"""

import pytest
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.app.recognition.detector import UIElement
from backend.app.codegen.html import HTMLCodeGenerator
from backend.app.codegen.react import ReactCodeGenerator, ReactGenerationError
from backend.app.codegen.swift import Swift<PERSON><PERSON>odeGenerator, SwiftUIGenerationError

class TestHTMLCodeGenerator:
    """Test cases for HTML code generator."""
    
    def test_initialization(self):
        """Test HTML generator initialization."""
        generator = HTMLCodeGenerator()
        assert generator is not None
        assert generator.framework == 'tailwind'
        assert generator.responsive is True
    
    def test_generate_simple_element(self, sample_ui_element):
        """Test generating HTML for a simple element."""
        generator = HTMLCodeGenerator()
        
        result = generator.generate(sample_ui_element)
        
        assert isinstance(result, dict)
        assert 'html' in result
        assert 'css' in result
        assert 'framework' in result
        
        html = result['html']
        assert '<button' in html
        assert 'Test Button' in html
    
    def test_generate_hierarchy(self, sample_ui_hierarchy):
        """Test generating HTML for element hierarchy."""
        generator = HTMLCodeGenerator()
        
        result = generator.generate(sample_ui_hierarchy)
        
        html = result['html']
        assert '<div' in html  # Container
        assert '<button' in html  # Button
        assert '<input' in html  # Input
        assert '<p' in html  # Text

class TestReactCodeGenerator:
    """Test cases for React code generator."""
    
    def test_initialization_default(self):
        """Test React generator default initialization."""
        generator = ReactCodeGenerator()
        assert generator.styling == 'tailwind'
        assert generator.typescript is True
    
    def test_initialization_custom(self):
        """Test React generator custom initialization."""
        generator = ReactCodeGenerator(styling='vanilla', typescript=False)
        assert generator.styling == 'vanilla'
        assert generator.typescript is False
    
    def test_initialization_invalid_styling(self):
        """Test React generator with invalid styling."""
        with pytest.raises(ValueError):
            ReactCodeGenerator(styling='invalid_framework')
    
    def test_generate_empty_element(self):
        """Test generating React code with empty element."""
        generator = ReactCodeGenerator()
        
        with pytest.raises(ReactGenerationError):
            generator.generate(None)
    
    def test_generate_simple_element(self, sample_ui_element):
        """Test generating React code for a simple element."""
        generator = ReactCodeGenerator()
        
        result = generator.generate(sample_ui_element)
        
        assert isinstance(result, dict)
        assert 'code' in result
        assert 'language' in result
        assert 'framework' in result
        assert 'styling' in result
        
        code = result['code']
        assert 'import React' in code
        assert 'const GeneratedComponent' in code
        assert 'export default' in code
        assert '<button' in code
    
    def test_generate_hierarchy(self, sample_ui_hierarchy):
        """Test generating React code for element hierarchy."""
        generator = ReactCodeGenerator()
        
        result = generator.generate(sample_ui_hierarchy)
        
        code = result['code']
        assert 'import React' in code
        assert '<div' in code  # Container
        assert '<button' in code  # Button
        assert '<input' in code  # Input
        assert '<p' in code  # Text
    
    def test_generate_with_typescript(self, sample_ui_element):
        """Test generating TypeScript React code."""
        generator = ReactCodeGenerator(typescript=True)
        
        result = generator.generate(sample_ui_element)
        
        code = result['code']
        assert 'React.FC' in code
        assert result['language'] == 'typescript'
    
    def test_generate_with_javascript(self, sample_ui_element):
        """Test generating JavaScript React code."""
        generator = ReactCodeGenerator(typescript=False)
        
        result = generator.generate(sample_ui_element)
        
        code = result['code']
        assert 'React.FC' not in code
        assert result['language'] == 'javascript'
    
    def test_generate_with_different_styling(self, sample_ui_element):
        """Test generating React code with different styling frameworks."""
        styling_options = ['tailwind', 'vanilla', 'css-modules']
        
        for styling in styling_options:
            generator = ReactCodeGenerator(styling=styling)
            result = generator.generate(sample_ui_element)
            
            assert result['styling'] == styling
            code = result['code']
            
            if styling == 'tailwind':
                assert 'className=' in code
            elif styling == 'css-modules':
                assert 'styles.' in code or 'className=' in code
    
    def test_generate_with_options(self, sample_ui_element):
        """Test generating React code with various options."""
        generator = ReactCodeGenerator()
        
        options = {
            'responsive': True,
            'accessibility': True,
            'add_click_handlers': True
        }
        
        result = generator.generate(sample_ui_element, options)
        
        code = result['code']
        assert 'onClick=' in code  # Click handler should be added

class TestSwiftUICodeGenerator:
    """Test cases for SwiftUI code generator."""
    
    def test_initialization(self):
        """Test SwiftUI generator initialization."""
        generator = SwiftUICodeGenerator()
        assert generator is not None
    
    def test_generate_empty_element(self):
        """Test generating SwiftUI code with empty element."""
        generator = SwiftUICodeGenerator()
        
        with pytest.raises(SwiftUIGenerationError):
            generator.generate(None)
    
    def test_generate_simple_element(self, sample_ui_element):
        """Test generating SwiftUI code for a simple element."""
        generator = SwiftUICodeGenerator()
        
        result = generator.generate(sample_ui_element)
        
        assert isinstance(result, dict)
        assert 'code' in result
        assert 'language' in result
        assert 'framework' in result
        
        code = result['code']
        assert 'import SwiftUI' in code
        assert 'struct GeneratedView: View' in code
        assert 'var body: some View' in code
        assert 'Button(' in code
    
    def test_generate_hierarchy(self, sample_ui_hierarchy):
        """Test generating SwiftUI code for element hierarchy."""
        generator = SwiftUICodeGenerator()
        
        result = generator.generate(sample_ui_hierarchy)
        
        code = result['code']
        assert 'import SwiftUI' in code
        assert 'VStack' in code or 'HStack' in code  # Container
        assert 'Button(' in code  # Button
        assert 'TextField(' in code  # Input
        assert 'Text(' in code  # Text
    
    def test_generate_with_preview(self, sample_ui_element):
        """Test that SwiftUI code includes preview."""
        generator = SwiftUICodeGenerator()
        
        result = generator.generate(sample_ui_element)
        
        code = result['code']
        assert 'PreviewProvider' in code
        assert 'static var previews' in code
    
    def test_generate_different_element_types(self):
        """Test generating SwiftUI code for different element types."""
        generator = SwiftUICodeGenerator()
        
        # Test button
        button = UIElement(type='button', bbox=(0, 0, 100, 50), text='Button')
        result = generator.generate(button)
        assert 'Button(' in result['code']
        
        # Test text
        text = UIElement(type='text', bbox=(0, 0, 100, 20), text='Text')
        result = generator.generate(text)
        assert 'Text(' in result['code']
        
        # Test input
        input_elem = UIElement(type='input', bbox=(0, 0, 200, 30), text='Placeholder')
        result = generator.generate(input_elem)
        assert 'TextField(' in result['code']
