"""
Test configuration and fixtures for SnapCode test suite.
"""

import pytest
import numpy as np
import base64
import sys
from pathlib import Path
from io import BytesIO
from PIL import Image
from fastapi.testclient import TestClient

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.app.api import app
from backend.app.recognition.detector import UIElement
from backend.app.config import settings

@pytest.fixture
def client():
    """FastAPI test client."""
    return TestClient(app)

@pytest.fixture
def sample_image_array():
    """Sample image as numpy array for testing."""
    # Create a simple test image
    image = np.zeros((100, 200, 3), dtype=np.uint8)
    # Add some colored rectangles to simulate UI elements
    image[20:40, 20:80] = [255, 0, 0]  # Red rectangle (button-like)
    image[50:70, 20:180] = [0, 255, 0]  # Green rectangle (input-like)
    image[80:90, 20:180] = [0, 0, 255]  # Blue rectangle (text-like)
    return image

@pytest.fixture
def sample_base64_image(sample_image_array):
    """Sample image as base64 string for API testing."""
    # Convert numpy array to PIL Image
    pil_image = Image.fromarray(sample_image_array)
    
    # Convert to base64
    buffer = BytesIO()
    pil_image.save(buffer, format='PNG')
    buffer.seek(0)
    
    base64_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
    return base64_data

@pytest.fixture
def sample_ui_element():
    """Sample UI element for testing."""
    element = UIElement(
        type='button',
        bbox=(10, 20, 100, 40),
        text='Test Button',
        style={'background-color': 'rgb(255, 0, 0)', 'color': 'white'},
        confidence=0.95
    )
    return element

@pytest.fixture
def sample_ui_hierarchy():
    """Sample UI element hierarchy for testing."""
    # Root container
    root = UIElement(
        type='container',
        bbox=(0, 0, 200, 150),
        style={'align': 'vertical'}
    )
    
    # Button child
    button = UIElement(
        type='button',
        bbox=(20, 20, 80, 40),
        text='Click Me',
        style={'background-color': 'rgb(59, 130, 246)', 'color': 'white'},
        confidence=0.9
    )
    
    # Input child
    input_elem = UIElement(
        type='input',
        bbox=(20, 70, 160, 30),
        text='Enter text here',
        style={'border': '1px solid #ccc'},
        confidence=0.85
    )
    
    # Text child
    text_elem = UIElement(
        type='text',
        bbox=(20, 110, 160, 20),
        text='Sample text content',
        style={'color': 'black'},
        confidence=0.8
    )
    
    root.children = [button, input_elem, text_elem]
    return root

@pytest.fixture
def conversion_request_data(sample_base64_image):
    """Sample conversion request data."""
    return {
        "image": sample_base64_image,
        "framework": "react",
        "options": {
            "styling": "tailwind",
            "typescript": True,
            "responsive": True,
            "accessibility": True,
            "optimize": True,
            "include_comments": True
        }
    }

@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """Setup test environment configuration."""
    # Override settings for testing
    settings.environment = "testing"
    settings.debug = True
    settings.rate_limit_enabled = False
    settings.redis_enabled = False
