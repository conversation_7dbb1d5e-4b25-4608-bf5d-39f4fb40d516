{"backend/app/tests/test_api.py::test_health_check": true, "backend/app/tests/test_api.py::test_root_endpoint": true, "backend/app/tests/test_api.py::test_get_supported_frameworks": true, "backend/app/tests/test_api.py::test_get_api_status": true, "backend/app/tests/test_api.py::test_convert_screenshot_success": true, "backend/app/tests/test_api.py::test_convert_screenshot_invalid_image": true, "backend/app/tests/test_api.py::test_convert_screenshot_missing_image": true, "backend/app/tests/test_api.py::test_convert_screenshot_different_frameworks": true, "backend/app/tests/test_api.py::test_convert_screenshot_different_styling_options": true, "backend/app/tests/test_api.py::test_request_headers": true, "backend/app/tests/test_api.py::test_cors_headers": true, "backend/app/tests/test_api.py::test_error_handling": true, "backend/app/tests/test_api.py::test_debug_endpoints_in_debug_mode": true}