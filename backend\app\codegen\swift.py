"""
SnapCode SwiftUI Code Generator

This module generates SwiftUI code from UI elements with proper Swift syntax,
iOS design patterns, and modern SwiftUI best practices.
"""

import logging
from typing import List, Dict, Optional, Any
from functools import lru_cache
from ..recognition.detector import UIElement
from ..utils.style import StyleExtractor

class SwiftUIGenerationError(Exception):
    """Custom exception for SwiftUI code generation errors."""
    pass

class SwiftUICodeGenerator:
    """Generates SwiftUI code from UI elements with iOS design patterns."""
    
    def __init__(self, cache_size: int = 128):
        """
        Initialize the SwiftUI code generator.
        
        Args:
            cache_size: Size of the LRU cache for view mappings
        """
        self.indent = '    '
        self.logger = logging.getLogger(__name__)
        self.style_extractor = StyleExtractor()
        self.view_counter = 0
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup logging configuration."""
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def generate(self, root: UIElement, options: Dict[str, Any] = None) -> Dict[str, str]:
        """
        Generate SwiftUI code from UI element hierarchy.
        
        Args:
            root: Root UI element
            options: Generation options
            
        Returns:
            Dictionary containing generated code and metadata
        """
        try:
            if not root:
                raise SwiftUIGenerationError("Root element is required")
            
            options = options or {}
            self.view_counter = 0
            
            self.logger.info("Starting SwiftUI generation")
            
            # Generate imports
            imports = self._generate_imports(options)
            
            # Generate main view
            view_code = self._generate_view(root, 'GeneratedView', options)
            
            # Generate preview
            preview_code = self._generate_preview('GeneratedView', options)
            
            # Combine all parts
            full_code = '\n'.join([
                imports,
                '',
                view_code,
                '',
                preview_code
            ])
            
            return {
                'code': full_code,
                'language': 'swift',
                'framework': 'swiftui',
                'views_generated': self.view_counter
            }
            
        except Exception as e:
            self.logger.error(f"SwiftUI generation failed: {str(e)}")
            raise SwiftUIGenerationError(f"Generation failed: {str(e)}")
    
    def _generate_imports(self, options: Dict[str, Any]) -> str:
        """Generate import statements."""
        imports = ['import SwiftUI']
        
        # Add additional imports based on options
        if options.get('use_combine', False):
            imports.append('import Combine')
        
        if options.get('use_foundation', False):
            imports.append('import Foundation')
        
        return '\n'.join(imports)
    
    def _generate_view(self, element: UIElement, name: str, options: Dict[str, Any]) -> str:
        """Generate a SwiftUI view from a UI element."""
        self.view_counter += 1
        
        lines = []
        
        # View declaration
        lines.append(f"struct {name}: View {{")
        
        # State variables if needed
        state_vars = self._generate_state_variables(element, options)
        if state_vars:
            lines.extend([f"{self.indent}{var}" for var in state_vars])
            lines.append('')
        
        # Body property
        lines.append(f"{self.indent}var body: some View {{")
        
        # Generate view body
        body_lines = self._generate_view_body(element, 2, options)
        lines.extend(body_lines)
        
        lines.append(f"{self.indent}}}")
        lines.append("}")
        
        return '\n'.join(lines)
    
    def _generate_state_variables(self, element: UIElement, options: Dict[str, Any]) -> List[str]:
        """Generate @State variables for interactive elements."""
        state_vars = []
        
        # Check if we need state for buttons or inputs
        if self._has_interactive_elements(element):
            state_vars.append("@State private var isPressed = false")
        
        if self._has_text_inputs(element):
            state_vars.append("@State private var textInput = \"\"")
        
        return state_vars
    
    def _has_interactive_elements(self, element: UIElement) -> bool:
        """Check if element hierarchy contains interactive elements."""
        if element.type.lower() in ['button']:
            return True
        
        return any(self._has_interactive_elements(child) for child in element.children)
    
    def _has_text_inputs(self, element: UIElement) -> bool:
        """Check if element hierarchy contains text inputs."""
        if element.type.lower() in ['input', 'textfield']:
            return True
        
        return any(self._has_text_inputs(child) for child in element.children)
    
    def _generate_view_body(self, element: UIElement, depth: int, options: Dict[str, Any]) -> List[str]:
        """Generate the body of a SwiftUI view."""
        if not element:
            return []
        
        lines = []
        indent = self.indent * depth
        
        # Generate SwiftUI view
        view_lines = self._generate_swiftui_element(element, depth, options)
        lines.extend(view_lines)
        
        return lines
    
    def _generate_swiftui_element(self, element: UIElement, depth: int, options: Dict[str, Any]) -> List[str]:
        """Generate SwiftUI code for a UI element and its children."""
        if not element:
            return []
        
        lines = []
        indent = self.indent * depth
        
        element_type = element.type.lower()
        
        # Generate appropriate SwiftUI view
        if element_type == 'button':
            lines.extend(self._generate_button(element, depth, options))
        
        elif element_type == 'text':
            lines.extend(self._generate_text(element, depth, options))
        
        elif element_type == 'input':
            lines.extend(self._generate_textfield(element, depth, options))
        
        elif element_type == 'image':
            lines.extend(self._generate_image(element, depth, options))
        
        elif element_type == 'container':
            lines.extend(self._generate_container(element, depth, options))
        
        else:
            # Default container
            lines.extend(self._generate_container(element, depth, options))
        
        return lines
    
    def _generate_button(self, element: UIElement, depth: int, options: Dict[str, Any]) -> List[str]:
        """Generate SwiftUI Button view."""
        lines = []
        indent = self.indent * depth
        
        # Button with action
        lines.append(f"{indent}Button(action: {{")
        lines.append(f"{indent}{self.indent}// Button action")
        lines.append(f"{indent}{self.indent}print(\"Button tapped\")")
        lines.append(f"{indent}}}) {{")
        
        # Button label
        if element.text and element.text.strip():
            text_content = self._escape_swift_string(element.text.strip())
            lines.append(f"{indent}{self.indent}Text(\"{text_content}\")")
        else:
            lines.append(f"{indent}{self.indent}Text(\"Button\")")
        
        # Apply button styling
        styling_lines = self._generate_button_styling(element, depth + 1, options)
        lines.extend(styling_lines)
        
        lines.append(f"{indent}}}")
        
        return lines
    
    def _generate_text(self, element: UIElement, depth: int, options: Dict[str, Any]) -> List[str]:
        """Generate SwiftUI Text view."""
        lines = []
        indent = self.indent * depth
        
        if element.text and element.text.strip():
            text_content = self._escape_swift_string(element.text.strip())
            lines.append(f"{indent}Text(\"{text_content}\")")
        else:
            lines.append(f"{indent}Text(\"Sample Text\")")
        
        # Apply text styling
        styling_lines = self._generate_text_styling(element, depth, options)
        lines.extend(styling_lines)
        
        return lines
    
    def _generate_textfield(self, element: UIElement, depth: int, options: Dict[str, Any]) -> List[str]:
        """Generate SwiftUI TextField view."""
        lines = []
        indent = self.indent * depth
        
        placeholder = element.text.strip() if element.text else "Enter text"
        placeholder = self._escape_swift_string(placeholder)
        
        lines.append(f"{indent}TextField(\"{placeholder}\", text: $textInput)")
        
        # Apply text field styling
        styling_lines = self._generate_textfield_styling(element, depth, options)
        lines.extend(styling_lines)
        
        return lines
    
    def _generate_image(self, element: UIElement, depth: int, options: Dict[str, Any]) -> List[str]:
        """Generate SwiftUI Image view."""
        lines = []
        indent = self.indent * depth
        
        lines.append(f"{indent}Image(systemName: \"photo\")")
        
        # Apply image styling
        styling_lines = self._generate_image_styling(element, depth, options)
        lines.extend(styling_lines)
        
        return lines
    
    def _generate_container(self, element: UIElement, depth: int, options: Dict[str, Any]) -> List[str]:
        """Generate SwiftUI container view (VStack, HStack, or ZStack)."""
        lines = []
        indent = self.indent * depth
        
        # Determine container type based on layout
        container_type = self._determine_container_type(element)
        
        if not element.children:
            lines.append(f"{indent}Rectangle()")
            lines.append(f"{indent}{self.indent}.fill(Color.gray.opacity(0.1))")
            lines.append(f"{indent}{self.indent}.frame(height: 100)")
            return lines
        
        # Container opening
        if container_type == 'VStack':
            lines.append(f"{indent}VStack(alignment: .leading, spacing: 8) {{")
        elif container_type == 'HStack':
            lines.append(f"{indent}HStack(alignment: .center, spacing: 8) {{")
        else:
            lines.append(f"{indent}VStack(spacing: 8) {{")
        
        # Generate children
        for child in element.children:
            child_lines = self._generate_swiftui_element(child, depth + 1, options)
            lines.extend(child_lines)
        
        lines.append(f"{indent}}}")
        
        # Apply container styling
        styling_lines = self._generate_container_styling(element, depth, options)
        lines.extend(styling_lines)
        
        return lines
    
    def _determine_container_type(self, element: UIElement) -> str:
        """Determine the appropriate SwiftUI container type."""
        if element.style.get('align') == 'horizontal':
            return 'HStack'
        elif element.style.get('align') == 'vertical':
            return 'VStack'
        else:
            # Default to VStack for most layouts
            return 'VStack'
    
    def _generate_button_styling(self, element: UIElement, depth: int, options: Dict[str, Any]) -> List[str]:
        """Generate styling modifiers for Button."""
        lines = []
        indent = self.indent * depth
        
        # Default button styling
        lines.append(f"{indent}.padding()")
        lines.append(f"{indent}.background(Color.blue)")
        lines.append(f"{indent}.foregroundColor(.white)")
        lines.append(f"{indent}.cornerRadius(8)")
        
        return lines
    
    def _generate_text_styling(self, element: UIElement, depth: int, options: Dict[str, Any]) -> List[str]:
        """Generate styling modifiers for Text."""
        lines = []
        indent = self.indent * depth
        
        # Basic text styling
        lines.append(f"{indent}.font(.body)")
        lines.append(f"{indent}.foregroundColor(.primary)")
        
        return lines
    
    def _generate_textfield_styling(self, element: UIElement, depth: int, options: Dict[str, Any]) -> List[str]:
        """Generate styling modifiers for TextField."""
        lines = []
        indent = self.indent * depth
        
        # TextField styling
        lines.append(f"{indent}.textFieldStyle(RoundedBorderTextFieldStyle())")
        lines.append(f"{indent}.padding(.horizontal)")
        
        return lines
    
    def _generate_image_styling(self, element: UIElement, depth: int, options: Dict[str, Any]) -> List[str]:
        """Generate styling modifiers for Image."""
        lines = []
        indent = self.indent * depth
        
        # Image styling
        lines.append(f"{indent}.resizable()")
        lines.append(f"{indent}.aspectRatio(contentMode: .fit)")
        lines.append(f"{indent}.frame(width: 100, height: 100)")
        
        return lines
    
    def _generate_container_styling(self, element: UIElement, depth: int, options: Dict[str, Any]) -> List[str]:
        """Generate styling modifiers for container views."""
        lines = []
        indent = self.indent * depth
        
        # Container styling
        lines.append(f"{indent}.padding()")
        
        return lines
    
    def _generate_preview(self, view_name: str, options: Dict[str, Any]) -> str:
        """Generate SwiftUI preview code."""
        lines = []
        
        lines.append(f"struct {view_name}_Previews: PreviewProvider {{")
        lines.append(f"{self.indent}static var previews: some View {{")
        lines.append(f"{self.indent}{self.indent}{view_name}()")
        lines.append(f"{self.indent}}}")
        lines.append("}")
        
        return '\n'.join(lines)
    
    def _escape_swift_string(self, text: str) -> str:
        """Escape string content for Swift."""
        return text.replace('\\', '\\\\').replace('"', '\\"').replace('\n', '\\n')
