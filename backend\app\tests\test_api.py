"""
Integration tests for SnapCode API endpoints.
"""

import pytest
from fastapi import status
from fastapi.testclient import TestClient

def test_health_check(client: TestClient):
    """Test health check endpoint."""
    response = client.get("/health")
    assert response.status_code == status.HTTP_200_OK
    
    data = response.json()
    assert data["status"] == "healthy"
    assert "version" in data
    assert "timestamp" in data
    assert "uptime" in data

def test_root_endpoint(client: TestClient):
    """Test root endpoint."""
    response = client.get("/")
    assert response.status_code == status.HTTP_200_OK
    
    data = response.json()
    assert "name" in data
    assert "version" in data
    assert "description" in data

def test_get_supported_frameworks(client: TestClient):
    """Test supported frameworks endpoint."""
    response = client.get("/api/frameworks")
    assert response.status_code == status.HTTP_200_OK
    
    data = response.json()
    assert "frameworks" in data
    assert "default" in data
    assert isinstance(data["frameworks"], list)
    assert len(data["frameworks"]) > 0

def test_get_api_status(client: TestClient):
    """Test API status endpoint."""
    response = client.get("/api/status")
    assert response.status_code == status.HTTP_200_OK
    
    data = response.json()
    assert data["status"] == "operational"
    assert "version" in data
    assert "environment" in data
    assert "rate_limit" in data
    assert "supported_frameworks" in data
    assert "max_file_size_mb" in data
    assert "allowed_image_types" in data

def test_convert_screenshot_success(client: TestClient, conversion_request_data):
    """Test successful screenshot conversion."""
    response = client.post("/api/convert", json=conversion_request_data)
    assert response.status_code == status.HTTP_200_OK
    
    data = response.json()
    assert data["success"] is True
    assert "result" in data
    assert "request_id" in data
    
    result = data["result"]
    assert "code" in result
    assert "framework" in result
    assert "components" in result
    assert "metadata" in result
    
    # Verify the generated code contains expected React elements
    code = result["code"]
    assert "import React" in code
    assert "const GeneratedComponent" in code
    assert "export default" in code

def test_convert_screenshot_invalid_image(client: TestClient):
    """Test conversion with invalid image data."""
    invalid_request = {
        "image": "invalid_base64_data",
        "framework": "react",
        "options": {
            "styling": "tailwind",
            "typescript": True
        }
    }
    
    response = client.post("/api/convert", json=invalid_request)
    assert response.status_code == status.HTTP_200_OK
    
    data = response.json()
    assert data["success"] is False
    assert "error" in data

def test_convert_screenshot_missing_image(client: TestClient):
    """Test conversion with missing image data."""
    invalid_request = {
        "framework": "react",
        "options": {
            "styling": "tailwind",
            "typescript": True
        }
    }
    
    response = client.post("/api/convert", json=invalid_request)
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

def test_convert_screenshot_different_frameworks(client: TestClient, conversion_request_data):
    """Test conversion with different frameworks."""
    frameworks = ["html", "react", "swift"]
    
    for framework in frameworks:
        request_data = conversion_request_data.copy()
        request_data["framework"] = framework
        
        response = client.post("/api/convert", json=request_data)
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        if data["success"]:
            assert data["result"]["framework"] == framework

def test_convert_screenshot_different_styling_options(client: TestClient, conversion_request_data):
    """Test conversion with different styling options."""
    styling_options = ["tailwind", "vanilla"]
    
    for styling in styling_options:
        request_data = conversion_request_data.copy()
        request_data["options"]["styling"] = styling
        
        response = client.post("/api/convert", json=request_data)
        assert response.status_code == status.HTTP_200_OK

def test_request_headers(client: TestClient):
    """Test that proper headers are added to responses."""
    response = client.get("/health")
    
    # Check for custom headers
    assert "X-Process-Time" in response.headers
    assert "X-Request-ID" in response.headers

def test_cors_headers(client: TestClient):
    """Test CORS headers are properly set."""
    response = client.options("/api/convert")
    
    # CORS headers should be present
    assert "access-control-allow-origin" in response.headers or response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED

@pytest.mark.skipif(True, reason="Rate limiting disabled in test environment")
def test_rate_limiting(client: TestClient, conversion_request_data):
    """Test rate limiting functionality."""
    # This test would be enabled in environments where rate limiting is active
    pass

def test_error_handling(client: TestClient):
    """Test error handling for invalid endpoints."""
    response = client.get("/nonexistent-endpoint")
    assert response.status_code == status.HTTP_404_NOT_FOUND

def test_debug_endpoints_in_debug_mode(client: TestClient):
    """Test debug endpoints are available in debug mode."""
    response = client.get("/debug/test-detection")
    # Should be available in debug mode
    assert response.status_code in [status.HTTP_200_OK, status.HTTP_404_NOT_FOUND]
    
    response = client.get("/debug/test-generation")
    # Should be available in debug mode
    assert response.status_code in [status.HTTP_200_OK, status.HTTP_404_NOT_FOUND]
