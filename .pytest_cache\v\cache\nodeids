["backend/app/tests/test_api.py::test_convert_screenshot_different_frameworks", "backend/app/tests/test_api.py::test_convert_screenshot_different_styling_options", "backend/app/tests/test_api.py::test_convert_screenshot_invalid_image", "backend/app/tests/test_api.py::test_convert_screenshot_missing_image", "backend/app/tests/test_api.py::test_convert_screenshot_success", "backend/app/tests/test_api.py::test_cors_headers", "backend/app/tests/test_api.py::test_debug_endpoints_in_debug_mode", "backend/app/tests/test_api.py::test_error_handling", "backend/app/tests/test_api.py::test_get_api_status", "backend/app/tests/test_api.py::test_get_supported_frameworks", "backend/app/tests/test_api.py::test_health_check", "backend/app/tests/test_api.py::test_rate_limiting", "backend/app/tests/test_api.py::test_request_headers", "backend/app/tests/test_api.py::test_root_endpoint", "backend/app/tests/test_code_generation.py::TestHTMLCodeGenerator::test_generate_hierarchy", "backend/app/tests/test_code_generation.py::TestHTMLCodeGenerator::test_generate_simple_element", "backend/app/tests/test_code_generation.py::TestHTMLCodeGenerator::test_initialization", "backend/app/tests/test_code_generation.py::TestReactCodeGenerator::test_generate_empty_element", "backend/app/tests/test_code_generation.py::TestReactCodeGenerator::test_generate_hierarchy", "backend/app/tests/test_code_generation.py::TestReactCodeGenerator::test_generate_simple_element", "backend/app/tests/test_code_generation.py::TestReactCodeGenerator::test_generate_with_different_styling", "backend/app/tests/test_code_generation.py::TestReactCodeGenerator::test_generate_with_javascript", "backend/app/tests/test_code_generation.py::TestReactCodeGenerator::test_generate_with_options", "backend/app/tests/test_code_generation.py::TestReactCodeGenerator::test_generate_with_typescript", "backend/app/tests/test_code_generation.py::TestReactCodeGenerator::test_initialization_custom", "backend/app/tests/test_code_generation.py::TestReactCodeGenerator::test_initialization_default", "backend/app/tests/test_code_generation.py::TestReactCodeGenerator::test_initialization_invalid_styling", "backend/app/tests/test_code_generation.py::TestSwiftUICodeGenerator::test_generate_different_element_types", "backend/app/tests/test_code_generation.py::TestSwiftUICodeGenerator::test_generate_empty_element", "backend/app/tests/test_code_generation.py::TestSwiftUICodeGenerator::test_generate_hierarchy", "backend/app/tests/test_code_generation.py::TestSwiftUICodeGenerator::test_generate_simple_element", "backend/app/tests/test_code_generation.py::TestSwiftUICodeGenerator::test_generate_with_preview", "backend/app/tests/test_code_generation.py::TestSwiftUICodeGenerator::test_initialization", "backend/app/tests/test_image_processing.py::TestImageProcessor::test_decode_base64_image_empty_data", "backend/app/tests/test_image_processing.py::TestImageProcessor::test_decode_base64_image_invalid_data", "backend/app/tests/test_image_processing.py::TestImageProcessor::test_decode_base64_image_success", "backend/app/tests/test_image_processing.py::TestImageProcessor::test_decode_base64_image_with_data_url", "backend/app/tests/test_image_processing.py::TestImageProcessor::test_encode_image_to_base64", "backend/app/tests/test_image_processing.py::TestImageProcessor::test_enhance_image", "backend/app/tests/test_image_processing.py::TestImageProcessor::test_error_handling_in_enhancement", "backend/app/tests/test_image_processing.py::TestImageProcessor::test_get_image_info", "backend/app/tests/test_image_processing.py::TestImageProcessor::test_get_image_info_grayscale", "backend/app/tests/test_image_processing.py::TestImageProcessor::test_initialization", "backend/app/tests/test_image_processing.py::TestImageProcessor::test_preprocess_for_detection", "backend/app/tests/test_image_processing.py::TestImageProcessor::test_supported_formats_validation", "backend/app/tests/test_image_processing.py::TestImageProcessor::test_validate_and_resize_large_image", "backend/app/tests/test_image_processing.py::TestImageProcessor::test_validate_and_resize_normal_image", "backend/app/tests/test_image_processing.py::TestImageProcessor::test_validate_and_resize_small_image"]